import { NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"
import { checkUserPermission } from "@/lib/permission-service"

/**
 * @swagger
 * /api/admin/roles:
 *   get:
 *     summary: 獲取所有角色列表
 *     tags: [Admin - Roles]
 *     responses:
 *       200:
 *         description: 成功獲取角色列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       role_key:
 *                         type: string
 *                       name:
 *                         type: string
 *                       description:
 *                         type: string
 *                       level:
 *                         type: integer
 *       401:
 *         description: 未授權
 *       403:
 *         description: 權限不足
 */
export async function GET(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = await createServerClient(cookieStore)

    // 檢查用戶是否已登入 - 使用 safeGetUser 處理 AuthSessionMissingError
    const { safeGetUser } = await import("@/lib/api-utils")
    const { user, error: userError, isAuthError } = await safeGetUser(supabase)

    if (userError || isAuthError) {
      return NextResponse.json({ success: false, error: "認證失敗，請重新登入" }, { status: 401 })
    }

    if (!user) {
      return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
    }

    // 檢查管理員權限
    const permissionCheck = await checkUserPermission(user.id, 'view_admin_panel')
    if (!permissionCheck.success || !permissionCheck.hasPermission) {
      return NextResponse.json(
        { success: false, error: "權限不足" },
        { status: 403 }
      )
    }

    // TODO: 暫時返回模擬數據，等 SQL 遷移完成後再使用真實數據
    // const { data: roles, error } = await supabase
    //   .from('roles')
    //   .select('*')
    //   .order('level', { ascending: false })

    // if (error) {
    //   console.error('獲取角色列表時出錯:', error)
    //   return NextResponse.json(
    //     { success: false, error: "獲取角色列表失敗" },
    //     { status: 500 }
    //   )
    // }

    // 模擬角色數據
    const mockRoles = [
      {
        role_key: 'owner',
        name: '站長',
        description: '系統最高管理員，擁有所有權限',
        level: 100
      },
      {
        role_key: 'topic_mod',
        name: '主題版主',
        description: '管理特定主題下的所有內容',
        level: 50
      },
      {
        role_key: 'subtopic_mod',
        name: '子主題版主',
        description: '管理特定子主題下的內容',
        level: 25
      }
    ]

    return NextResponse.json({
      success: true,
      data: mockRoles
    })

  } catch (error: any) {
    console.error('處理獲取角色列表請求時出錯:', error)
    return NextResponse.json(
      { success: false, error: "內部伺服器錯誤" },
      { status: 500 }
    )
  }
}

/**
 * @swagger
 * /api/admin/roles:
 *   post:
 *     summary: 指派角色給用戶
 *     tags: [Admin - Roles]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *               - roleKey
 *             properties:
 *               userId:
 *                 type: string
 *                 description: 目標用戶ID
 *               roleKey:
 *                 type: string
 *                 description: 角色鍵值
 *               expiresAt:
 *                 type: string
 *                 format: date-time
 *                 description: 過期時間（可選）
 *     responses:
 *       200:
 *         description: 成功指派角色
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未授權
 *       403:
 *         description: 權限不足
 */
export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = await createServerClient(cookieStore)

    // 檢查用戶是否已登入 - 使用 safeGetUser 處理 AuthSessionMissingError
    const { safeGetUser } = await import("@/lib/api-utils")
    const { user, error: userError, isAuthError } = await safeGetUser(supabase)

    if (userError || isAuthError) {
      return NextResponse.json({ success: false, error: "認證失敗，請重新登入" }, { status: 401 })
    }

    if (!user) {
      return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
    }

    // TODO: 暫時跳過權限檢查，等 SQL 遷移完成後再啟用
    // const permissionCheck = await checkUserPermission(user.id, 'manage_moderators')
    // if (!permissionCheck.success || !permissionCheck.hasPermission) {
    //   return NextResponse.json(
    //     { success: false, error: "權限不足" },
    //     { status: 403 }
    //   )
    // }

    // 解析請求體
    const body = await request.json()
    const { userId, roleKey, expiresAt } = body

    // 驗證必要參數
    if (!userId || !roleKey) {
      return NextResponse.json(
        { success: false, error: "缺少必要參數：userId 和 roleKey" },
        { status: 400 }
      )
    }

    // 驗證角色是否存在
    const { data: role, error: roleError } = await supabase
      .from('roles')
      .select('role_key')
      .eq('role_key', roleKey)
      .single()

    if (roleError || !role) {
      return NextResponse.json(
        { success: false, error: "無效的角色" },
        { status: 400 }
      )
    }

    // 驗證目標用戶是否存在
    const { data: targetUser, error: targetUserError } = await supabase
      .from('profiles')
      .select('id')
      .eq('id', userId)
      .single()

    if (targetUserError || !targetUser) {
      return NextResponse.json(
        { success: false, error: "目標用戶不存在" },
        { status: 400 }
      )
    }

    // 使用數據庫函數指派角色
    const { data, error } = await supabase
      .rpc('assign_role_to_user', {
        target_user_id: userId,
        role_key: roleKey,
        granted_by_user_id: user.id,
        expires_at: expiresAt || null
      })

    if (error) {
      console.error('指派角色時出錯:', error)
      return NextResponse.json(
        { success: false, error: error.message || "指派角色失敗" },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: "角色指派成功"
    })

  } catch (error: any) {
    console.error('處理指派角色請求時出錯:', error)
    return NextResponse.json(
      { success: false, error: "內部伺服器錯誤" },
      { status: 500 }
    )
  }
}

/**
 * @swagger
 * /api/admin/roles:
 *   delete:
 *     summary: 移除用戶角色
 *     tags: [Admin - Roles]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *               - roleKey
 *             properties:
 *               userId:
 *                 type: string
 *                 description: 目標用戶ID
 *               roleKey:
 *                 type: string
 *                 description: 角色鍵值
 *     responses:
 *       200:
 *         description: 成功移除角色
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未授權
 *       403:
 *         description: 權限不足
 */
export async function DELETE(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = await createServerClient(cookieStore)

    // 檢查用戶是否已登入 - 使用 safeGetUser 處理 AuthSessionMissingError
    const { safeGetUser } = await import("@/lib/api-utils")
    const { user, error: userError, isAuthError } = await safeGetUser(supabase)

    if (userError || isAuthError) {
      return NextResponse.json({ success: false, error: "認證失敗，請重新登入" }, { status: 401 })
    }

    if (!user) {
      return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
    }

    // TODO: 暫時跳過權限檢查，等 SQL 遷移完成後再啟用
    // const permissionCheck = await checkUserPermission(user.id, 'manage_moderators')
    // if (!permissionCheck.success || !permissionCheck.hasPermission) {
    //   return NextResponse.json(
    //     { success: false, error: "權限不足" },
    //     { status: 403 }
    //   )
    // }

    // 解析請求體
    const body = await request.json()
    const { userId, roleKey } = body

    // 驗證必要參數
    if (!userId || !roleKey) {
      return NextResponse.json(
        { success: false, error: "缺少必要參數：userId 和 roleKey" },
        { status: 400 }
      )
    }

    // 使用數據庫函數移除角色
    const { data, error } = await supabase
      .rpc('remove_role_from_user', {
        target_user_id: userId,
        role_key: roleKey,
        removed_by_user_id: user.id
      })

    if (error) {
      console.error('移除角色時出錯:', error)
      return NextResponse.json(
        { success: false, error: error.message || "移除角色失敗" },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: "角色移除成功"
    })

  } catch (error: any) {
    console.error('處理移除角色請求時出錯:', error)
    return NextResponse.json(
      { success: false, error: "內部伺服器錯誤" },
      { status: 500 }
    )
  }
}
