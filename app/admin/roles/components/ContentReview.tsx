"use client"

import { useState, useEffect } from "react"
import { ContentCard } from "@/components/content-card"
import { CardReviewModal } from "./CardReviewModal"
import { ReviewFilters } from "./ReviewFilters"
import { ReviewStats } from "./ReviewStats"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { useToast } from "@/components/ui/use-toast"
import { Loader2, CheckCircle, XCircle, RefreshCw } from "lucide-react"
import { cn } from "@/lib/utils"

interface PendingCard {
  contentType: "viewpoint"
  id: string // 保持為 string，因為是 UUID
  title: string
  content: string
  author: {
    id: string
    name: string
    avatar: string
    email: string
  }
  topics: Array<{
    id: string
    name: string
    slug: string
  }>
  subtopics: Array<{
    id: string
    name: string
    slug: string
    topic_id: string
  }>
  semanticType: string
  contribution_type?: string
  originalAuthor?: string
  originalSource?: string
  timestamp: string
  stats: {
    likes: number
    dislikes: number
    comments: number
    bookmarks: number
    views: number
  }
  status: string
  created_at: string
  updated_at: string
}

interface Filters {
  topicId?: string
  subtopicId?: string
  semanticType?: string
  sortBy?: 'newest' | 'oldest'
}

export function ContentReview() {
  const { toast } = useToast()
  const [pendingCards, setPendingCards] = useState<PendingCard[]>([])
  const [selectedCard, setSelectedCard] = useState<PendingCard | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [filters, setFilters] = useState<Filters>({})
  const [selectedCardIds, setSelectedCardIds] = useState<string[]>([])
  const [isBatchProcessing, setIsBatchProcessing] = useState(false)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
    hasMore: false
  })

  // 獲取待審核卡片
  const fetchPendingCards = async (page = 1, resetData = false) => {
    try {
      setIsLoading(true)
      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.limit.toString(),
        ...(filters.topicId && { topic_id: filters.topicId }),
        ...(filters.subtopicId && { subtopic_id: filters.subtopicId })
      })

      const response = await fetch(`/api/admin/content/pending-cards?${params}`)
      const data = await response.json()

      if (data.success) {
        if (resetData || page === 1) {
          setPendingCards(data.data.cards)
        } else {
          setPendingCards(prev => [...prev, ...data.data.cards])
        }
        setPagination(data.data.pagination)
      } else {
        toast({
          title: "載入失敗",
          description: data.error || "無法載入待審核卡片",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('獲取待審核卡片失敗:', error)
      toast({
        title: "載入失敗",
        description: "請稍後再試",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 處理卡片點擊 - 打開 Modal 而不是跳轉
  const handleCardClick = (card: PendingCard) => {
    setSelectedCard(card)
    setIsModalOpen(true)
  }

  // 將 UUID 轉換為數字 ID（用於 ContentCard 組件）
  const uuidToNumber = (uuid: string): number => {
    // 使用 UUID 的 hash 來生成一個數字 ID
    let hash = 0
    for (let i = 0; i < uuid.length; i++) {
      const char = uuid.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 轉換為 32 位整數
    }
    return Math.abs(hash)
  }

  // 處理審核完成
  const handleReviewed = () => {
    // 重新載入列表
    fetchPendingCards(1, true)
    setSelectedCardIds([]) // 清除選中狀態
  }

  // 處理篩選變更
  const handleFiltersChange = (newFilters: Filters) => {
    setFilters(newFilters)
    setPagination(prev => ({ ...prev, page: 1 }))
  }

  // 處理卡片選擇
  const handleCardSelect = (cardId: string, selected: boolean) => {
    if (selected) {
      setSelectedCardIds(prev => [...prev, cardId])
    } else {
      setSelectedCardIds(prev => prev.filter(id => id !== cardId))
    }
  }

  // 全選/取消全選
  const handleSelectAll = (selected: boolean) => {
    if (selected) {
      setSelectedCardIds(pendingCards.map(card => card.id))
    } else {
      setSelectedCardIds([])
    }
  }

  // 批量審核
  const handleBatchReview = async (action: 'approve' | 'reject') => {
    if (selectedCardIds.length === 0) {
      toast({
        title: "請選擇卡片",
        description: "請先選擇要審核的卡片",
        variant: "destructive"
      })
      return
    }

    setIsBatchProcessing(true)
    try {
      const response = await fetch('/api/admin/content/review-card', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          cardIds: selectedCardIds,
          action
        })
      })

      const data = await response.json()
      if (data.success) {
        toast({
          title: "批量審核完成",
          description: `成功處理 ${data.data.successCount} 個卡片${data.data.errorCount > 0 ? `，${data.data.errorCount} 個失敗` : ''}`
        })
        handleReviewed()
      } else {
        toast({
          title: "批量審核失敗",
          description: data.error || "請稍後再試",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('批量審核失敗:', error)
      toast({
        title: "批量審核失敗",
        description: "請稍後再試",
        variant: "destructive"
      })
    } finally {
      setIsBatchProcessing(false)
    }
  }

  // 載入更多
  const loadMore = () => {
    if (pagination.hasMore && !isLoading) {
      fetchPendingCards(pagination.page + 1)
    }
  }

  // 初始載入
  useEffect(() => {
    fetchPendingCards(1, true)
  }, [filters])

  return (
    <div className="space-y-6">
      {/* 統計信息 */}
      <ReviewStats 
        totalPending={pagination.total}
        selectedCount={selectedCardIds.length}
      />

      {/* 篩選器 */}
      <ReviewFilters 
        filters={filters}
        onFiltersChange={handleFiltersChange}
      />

      {/* 批量操作工具欄 */}
      {selectedCardIds.length > 0 && (
        <div className="flex items-center gap-4 p-4 bg-muted/50 rounded-lg border">
          <span className="text-sm font-medium">
            已選擇 {selectedCardIds.length} 個卡片
          </span>
          <div className="flex gap-2">
            <Button
              size="sm"
              onClick={() => handleBatchReview('approve')}
              disabled={isBatchProcessing}
              className="bg-green-600 hover:bg-green-700"
            >
              {isBatchProcessing ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <CheckCircle className="h-4 w-4 mr-2" />
              )}
              批量通過
            </Button>
            <Button
              size="sm"
              variant="destructive"
              onClick={() => handleBatchReview('reject')}
              disabled={isBatchProcessing}
            >
              {isBatchProcessing ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <XCircle className="h-4 w-4 mr-2" />
              )}
              批量拒絕
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => setSelectedCardIds([])}
            >
              取消選擇
            </Button>
          </div>
        </div>
      )}

      {/* 全選控制 */}
      {pendingCards.length > 0 && (
        <div className="flex items-center gap-2">
          <Checkbox
            id="select-all"
            checked={selectedCardIds.length === pendingCards.length}
            onCheckedChange={handleSelectAll}
          />
          <label htmlFor="select-all" className="text-sm font-medium cursor-pointer">
            全選 ({pendingCards.length} 個卡片)
          </label>
          <Button
            size="sm"
            variant="ghost"
            onClick={() => fetchPendingCards(1, true)}
            disabled={isLoading}
          >
            <RefreshCw className={cn("h-4 w-4", isLoading && "animate-spin")} />
          </Button>
        </div>
      )}

      {/* 卡片網格 - 使用與 /explore 相同的佈局 */}
      {isLoading && pendingCards.length === 0 ? (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">載入中...</span>
        </div>
      ) : pendingCards.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-lg text-muted-foreground">沒有待審核的卡片</p>
          <p className="text-sm text-muted-foreground mt-2">所有卡片都已處理完成</p>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {pendingCards.map((card) => (
              <div key={card.id} className="relative">
                {/* 選擇框 */}
                <div className="absolute top-2 left-2 z-10">
                  <Checkbox
                    checked={selectedCardIds.includes(card.id)}
                    onCheckedChange={(checked) => handleCardSelect(card.id, checked as boolean)}
                    className="bg-white/90 border-2"
                  />
                </div>
                
                {/* 卡片 */}
                <div
                  onClick={() => handleCardClick(card)}
                  className="cursor-pointer"
                >
                  <ContentCard
                    {...card}
                    id={parseInt(card.id.slice(-8), 16) || 0} // 將 UUID 轉換為數字 ID
                    topics={card.topics.map(t => t.name)} // 轉換為字符串數組
                    subtopics={card.subtopics.map(s => s.name)} // 轉換為字符串數組
                    variant="grid"
                    isInteractive={false} // 禁用鏈接跳轉和交互按鈕
                  />
                </div>
              </div>
            ))}
          </div>

          {/* 載入更多 */}
          {pagination.hasMore && (
            <div className="flex justify-center mt-8">
              <Button
                onClick={loadMore}
                disabled={isLoading}
                variant="outline"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    載入中...
                  </>
                ) : (
                  '載入更多'
                )}
              </Button>
            </div>
          )}
        </>
      )}

      {/* 審核 Modal */}
      <CardReviewModal
        card={selectedCard}
        open={isModalOpen}
        onOpenChange={setIsModalOpen}
        onReviewed={handleReviewed}
      />
    </div>
  )
}
